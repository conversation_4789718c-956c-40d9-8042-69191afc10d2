insert into dict_common(`type_code`,`dict_code`,`dict_desc`,`sort_no`) values('ColumnType','0','免费',1);
insert into dict_common(`type_code`,`dict_code`,`dict_desc`,`sort_no`) values('ColumnType','1','登录阅读',2);
insert into dict_common(`type_code`,`dict_code`,`dict_desc`,`sort_no`) values('ColumnType','2','限时免费',3);

-- 初始化标签
delete from tag where id > 0;
insert into `tag` (`id`, `tag_name`, `tag_type`, `category_id`, `status`, `deleted`)
values ('1', 'Java', '1', '1', '1', '0'),
       ('2', 'Go', '1', '1', '1', '0'),
       ('3', '算法', '1', '1', '1', '0'),
       ('4', 'Python', '1', '1', '1', '0'),
       ('5', 'Spring Boot', '1', '1', '1', '0'),
       ('6', '面试', '1', '1', '1', '0'),
       ('7', 'MySQL', '1', '1', '1', '0'),
       ('8', '数据库', '1', '1', '1', '0'),
       ('9', 'Spring', '1', '1', '1', '0'),
       ('10', '架构', '1', '1', '1', '0'),
       ('11', 'LeetCode', '1', '1', '1', '0'),
       ('12', 'Redis', '1', '1', '1', '0'),
       ('14', 'Linux', '1', '1', '1', '0'),
       ('15', 'JavaScript', '1', '2', '1', '0'),
       ('16', 'Vue.js', '1', '2', '1', '0'),
       ('17', 'React.js', '1', '2', '1', '0'),
       ('18', 'CSS', '1', '2', '1', '0'),
       ('20', 'TypeScript', '1', '2', '1', '0'),
       ('21', '后端', '1', '2', '1', '0'),
       ('22', 'Node.js', '1', '2', '1', '0'),
       ('23', '前端框架', '1', '2', '1', '0'),
       ('25', 'Webpack', '1', '2', '1', '0'),
       ('27', '微信小程序', '1', '2', '1', '0'),
       ('28', 'GitHub', '1', '2', '1', '0'),
       ('29', 'Kotlin', '1', '4', '1', '0'),
       ('30', 'Flutter', '1', '4', '1', '0'),
       ('33', 'Android Jetpack', '1', '4', '1', '0'),
       ('34', 'APP', '1', '4', '1', '0'),
       ('35', 'Android Studio', '1', '4', '1', '0'),
       ('36', '源码', '1', '4', '1', '0'),
       ('37', '性能优化', '1', '4', '1', '0'),
       ('40', 'gradle', '1', '4', '1', '0'),
       ('42', 'Swift', '1', '5', '1', '0'),
       ('43', 'SwiftUI', '1', '5', '1', '0'),
       ('48', 'Xcode', '1', '5', '1', '0'),
       ('49', 'Objective-C', '1', '5', '1', '0'),
       ('50', 'Mac', '1', '5', '1', '0'),
       ('51', 'WWDC', '1', '5', '1', '0'),
       ('52', '计算机视觉', '1', '5', '1', '0'),
       ('53', 'Apple', '1', '5', '1', '0'),
       ('54', '音视频开发', '1', '5', '1', '0'),
       ('55', '深度学习', '1', '6', '1', '0'),
       ('57', '机器学习', '1', '6', '1', '0'),
       ('61', 'PyTorch', '1', '6', '1', '0'),
       ('62', 'NLP', '1', '6', '1', '0'),
       ('63', '数据分析', '1', '6', '1', '0'),
       ('64', '神经网络', '1', '6', '1', '0'),
       ('65', 'TensorFlow', '1', '6', '1', '0'),
       ('66', '数据可视化', '1', '6', '1', '0'),
       ('67', '程序员', '1', '6', '1', '0'),
       ('68', '数据挖掘', '1', '6', '1', '0'),
       ('70', '前端', '1', '7', '1', '0'),
       ('71', '开源', '1', '7', '1', '0'),
       ('72', 'Git', '1', '7', '1', '0'),
       ('75', '测试', '1', '7', '1', '0'),
       ('78', '设计', '1', '7', '1', '0'),
       ('81', 'Unity3D', '1', '7', '1', '0'),
       ('82', 'Rust', '1', '7', '1', '0'),
       ('83', '大数据', '1', '7', '1', '0'),
       ('94', '年终总结', '1', '8', '1', '0'),
       ('97', '数据结构', '1', '8', '1', '0'),
       ('99', '云原生', '1', '9', '1', '0'),
       ('100', '笔记', '1', '9', '1', '0'),
       ('104', 'Serverless', '1', '9', '1', '0'),
       ('106', '容器', '1', '9', '1', '0'),
       ('107', '微服务', '1', '9', '1', '0'),
       ('109', '产品', '1', '9', '1', '0'),
       ('110', '产品经理', '1', '9', '1', '0'),
       ('112', 'RocketMQ', '1', '9', '1', '0'),
       ('114', 'sqlite', '1', '3', '1', '0'),
       ('115', 'sql', '1', '3', '1', '0'),
       ('116', 'spark', '1', '3', '1', '0'),
       ('117', 'hive', '1', '3', '1', '0'),
       ('118', 'hbase', '1', '3', '1', '0'),
       ('119', 'hdfs', '1', '3', '1', '0'),
       ('121', 'hadoop', '1', '3', '1', '0'),
       ('122', 'rabbitmq', '1', '3', '1', '0'),
       ('123', 'postgresql', '1', '3', '1', '0'),
       ('125', '数据仓库', '1', '3', '1', '0'),
       ('127', 'oracle', '1', '3', '1', '0'),
       ('128', 'flink', '1', '3', '1', '0'),
       ('129', 'nosql', '1', '3', '1', '0'),
       ('131', 'eureka', '1', '3', '1', '0'),
       ('132', 'mongodb', '1', '3', '1', '0'),
       ('133', 'zookeeper', '1', '3', '1', '0'),
       ('134', 'elasticsearch', '1', '3', '1', '0'),
       ('135', 'kafka', '1', '3', '1', '0'),
       ('136', 'json', '1', '3', '1', '0');