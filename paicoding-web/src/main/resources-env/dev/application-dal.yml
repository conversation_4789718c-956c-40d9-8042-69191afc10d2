spring:
  datasource: #单数据源
    url: ***************************/${database.name}?useUnicode=true&allowPublicKeyRetrieval=true&autoReconnect=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
    # fixme 注意：本地启动时，数据库用户名 + 密码，更新这里的配置
    username: root
    password: 11111111
  dynamic: # 动态数据源
   # primary: master # 这个表示默认的数据源，如果是注释状态，则表明使用上面的单数据源模式
    datasource:
      master:
        # 数据库名，从配置 database.name 中获取
        url: ***************************/${database.name}?useUnicode=true&allowPublicKeyRetrieval=true&autoReconnect=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: root
        password: 11111111
       # type: com.alibaba.druid.pool.DruidDataSource
        #DruidDataSource自有属性，下面的配置在IDEA中显示红色不影响项目启动
        filters: stat
        initialSize: 0
        minIdle: 1
        maxActive: 200
        maxWait: 10000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 200000
        testWhileIdle: true
        testOnBorrow: true
        validationQuery: select 1
      slave:
        # 数据库名，从配置 database.name 中获取
        url: ***************************/${database.name}?useUnicode=true&allowPublicKeyRetrieval=true&autoReconnect=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: root
        password:
  redis:
    host: localhost
    port: 6379
    password:

# elasticsearch配置
elasticsearch:
  # 是否开启ES？本地启动如果没有安装ES，可以设置为false关闭ES
  open: true
  # es集群名称
  clusterName: elasticsearch
  hosts: 127.0.0.1:9200
  userName: elastic
  password: elastic
  # es 请求方式
  scheme: http
  # es 连接超时时间
  connectTimeOut: 1000
  # es socket 连接超时时间
  socketTimeOut: 30000
  # es 请求超时时间
  connectionRequestTimeOut: 500
  # es 最大连接数
  maxConnectNum: 100
  # es 每个路由的最大连接数
  maxConnectNumPerRoute: 100




#
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志