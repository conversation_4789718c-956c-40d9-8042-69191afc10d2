环境安装
---

## jdk安装

```bash
# ubuntu
apt install openjdk-8-jdk

# centos
yum install openjdk-8-jdk
```

安装完毕之后，执行 `java` , `javac`命令进行验证

## maven安装

```bash
cd ~
mkdir soft
cd soft
wget https://dlcdn.apache.org/maven/maven-3/3.8.6/binaries/apache-maven-3.8.6-bin.tar.gz
tar -zxvf apache-maven-3.8.6-bin.tar.gz

vim ~/.bashrc

# 在最后添加环境变量
export M2_HOME=/home/<USER>/soft/apache-maven-3.8.6
PATH=$M2_HOME/bin:$PATH

# 配置生效
source ~/.bashrc
```

配置完成之后执行命令 `mvn --version` 进行验证

国内添加阿里的镜像源，加快下载速度

```bash
vim ~/soft/apache-maven-3.8.6/conf/settings.xml

# 在<mirros>标签中，添加下面的镜像源

    <mirror>
      <id>alimaven</id>
      <name>aliyun-maven</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
      <mirrorOf>central</mirrorOf>
    </mirror>
```

## nginx配置

配置访问域名

```bash
cd /usr/local/nginx/conf/

vim nginx.conf

# 添加子域名解析，每个域名一个独立的配置文件
# 在http的一级标签中，添加如下一行配置，表示在conf.d文件下的所有conf结尾的文件，都属于我们需要使用的nginx配置信息
include /usr/local/nginx/conf/conf.d/*.conf;
```

添加论坛的域名解析规则

```conf
vim conf.d/forum.conf


# 内容如下
upstream  forum_host {
    server 127.0.0.1:8080;
}
server {
    server_name forum.hhui.top;

    gzip on;
    gzip_buffers 32 4K;
    gzip_comp_level 6;
    gzip_min_length 100;
    gzip_types application/javascript text/css text/xml;
    gzip_disable "MSIE [1-6]\."; #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
    gzip_vary on;

    location ~* ^.+\.(ico|gif|jpg|jpeg|png)$ {
        access_log   off;
        expires      1d;
        proxy_pass         http://forum_host;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    }

    location ~* ^.+\.(css|js|txt|xml|swf|wav|pptx)$ {
        access_log   off;
        expires      10m;
        proxy_pass         http://forum_host;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
    }

    location / {
        proxy_set_header X-real-ip  $remote_addr;
        proxy_pass http://127.0.0.1:8080/;
        proxy_redirect default;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /usr/local/nginx/conf/conf.d/cert.pem;
    ssl_certificate_key /usr/local/nginx/conf/conf.d/key.pem;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* ******* valid=60s;
    resolver_timeout 2s;
}


server {
    if ($host = forum.hhui.top) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    server_name forum.hhui.top;
    return 404; # managed by Certbot
}
```

证书使用let's encrypt生成

## 数据库创建

```bash
# ubuntu
sudo apt-get install mysql-server

# centos
yum install mysql mysql-server mysql-libs
```

### 查询登录密码

```bash
grep "temporary password" /var/log/mysqld.log

## 输出如下
# A temporary password is generated for root@localhost: xxxx
```

### 密码修改:

使用`set password`

**格式：**

```
mysql> set password for 用户名@localhost = password('新密码');
```

**例子：**

```
mysql> set password for root@localhost = password('123');
```

update 方式

```
mysql> use mysql;

mysql> update user set password=password('123') where user='root' and host='localhost';

mysql> flush privileges;
```

添加用户

```
alter user 'root'@'localhost' identified by 'test';
create user 'test'@'%' IDENTIFIED BY 'test';
```

授予权限

```
# root 方式登录
grant all PRIVILEGES on test.* to 'yihui'@'%' IDENTIFIED by 'test';
flush privileges;
```

本项目在首次启动时，会自动创建数据库 + 表结构，无需额外操作；只是需要修改源码中的生产环境配置

## 配置调整

线上部署时，选择prod环境，因此需要设置对应的数据库相关配置信息

resources-env/prod/application-dal.yml

```yml
spring:
  datasource:
    url: ********************************************************************************************************
    username: xxx
    password: xxx
```

根据实际的情况进行修改

## 启动脚本

基于源码的部署脚本

```bash
#!/usr/bin/env bash

WEB_PATH="paicoding-web"
JAR_NAME="paicoding-web-0.0.1-SNAPSHOT.jar"

# 部署
function start() {
    git pull

    # 杀掉之前的进程
    cat pid.log| xargs -I {} kill {}
    mv ${JAR_NAME} ${JAR_NAME}.bak

    mvn clean install -Dmaven.test.skip=True -Pprod
    cd ${WEB_PATH}
    mvn clean package spring-boot:repackage -Dmaven.test.skip=true -Pprod
    cd -

    mv ${WEB_PATH}/target/${JAR_NAME} ./
    echo "启动脚本：==========="
    echo "nohup java -server -Xms512m -Xmx512m -Xmn512m -XX:NativeMemoryTracking=detail -XX:-OmitStackTraceInFastThrow -jar ${JAR_NAME} > /dev/null 2>&1 &"
    echo "==========="
    nohup java -server -Xms512m -Xmx512m -Xmn512m -XX:NativeMemoryTracking=detail -XX:-OmitStackTraceInFastThrow -jar ${JAR_NAME} > /dev/null 2>&1 &
    echo $! 1> pid.log
}

# 重启
function restart() {
    # 杀掉之前的进程
    cat pid.log| xargs -I {} kill {}
    # 重新启动
    echo "启动脚本：==========="
    echo "nohup java -server -Xms512m -Xmx512m -Xmn512m -XX:NativeMemoryTracking=detail -XX:-OmitStackTraceInFastThrow -jar ${JAR_NAME} > /dev/null 2>&1 &"
    echo "==========="
    nohup java -server -Xmn512m -Xmn512m -Xmn512m -XX:NativeMemoryTracking=detail -XX:-OmitStackTraceInFastThrow -jar ${JAR_NAME} > /dev/null 2>&1 &
    echo $! 1> pid.log
}

if [ $# == 0 ]; then
  echo "miss command: start | restart"
elif [ $1 == 'start' ]; then
  start
elif [ $1 == 'restart' ];then
  restart
else
  echo 'illegal command, support cmd: start | restart'
fi
```

启动命令

```bash
# 进入项目根目录，执行命令
# chmod +x launch.sh # 若脚本没有执行权限，则取消这行命令的注释，用于添加执行权限
./launch.sh start
```