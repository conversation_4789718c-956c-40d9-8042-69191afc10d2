# 本文记录服务器启动方式

## 0. 环境配置

服务器上启动之前，请先准备好相应的环境配置，请查看 [安装环境](安装环境.md) 文档进行服务器环境初始化

## 1. 源码方式构建
### 1.1 源码构建

进入工作目录，下来最新代码

```bash
cd /home/<USER>/workspace

<NAME_EMAIL>:itwanger/paicoding.git
```

线上部署时，选择prod环境，因此需要设置对应的数据库相关配置信息

- vim 进入 `resources-env/prod/application-dal.yml`

```yml
spring:
  datasource:
    url: ********************************************************************************************************
    username: xxx
    password: xxx
```

根据实际的情况进行修改ip, 用户名密码

接下来就是编译启动

```bash
cd paicoding
./launch.sh start
```

提示：

- 若launch.sh脚本没有执行权限，可以通过命令行 `chmod +x launch.sh` 添加
- 启动之后，可以发现当前目录下新增一个 `pid.log` 文件，里面记录的是启动的服务进程号
- 业务日志在当前目录的 `logs`下
  - 请求日志: logs/req-prod.log
  - 业务日志: logs/forum-prod.log


### 1.2. 应用重启

若只是单纯的希望应用重启一下

```java
cd /home/<USER>/workspace/paicoding

./launch.sh restart
```

### 1.3. 应用发布

当有新的改动时，若希望重新发布应用，执行下面的命令

```java
cd /home/<USER>/workspace/paicoding

./launch.sh start
```

## 2. jar包上传

首先确保服务器配置已准备完毕

接下来确保本地生产环境的数据库等相关配置已更新为正确的配置

然后就是再项目根目录下执行

```bash
# 打包jar，并上传到服务器，关闭旧的应用，重新启动新的应用
./deploy.sh prod
```