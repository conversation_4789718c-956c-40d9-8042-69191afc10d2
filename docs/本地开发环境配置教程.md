# 本地开发环境配置教程

## 1. 环境准备

首先准备好基础的开发环境，如

- jdk/jre: 请安装jdk8+以上版本
- maven: 本项目基于maven作为项目管理工具，因此在启动之前请配置好maven相关环境
- MySql数据库
    - 版本支持：8.x+
    - 说明：数据库可以使用本机的数据库，也可以使用非本机的（请注意本机能正常访问）
- git版本管理
- 开发工具：建议idea，当然eclipse/vs也没有问题

## 2. 项目启动

当环境准备完毕之后，接下来就是下载项目，导入开发工具进行启动演示

### 2.1 项目获取

本项目所有源码开源，因此您可以在github/gitee上免费获取

**通过git方式拉取项目**

```bash
# Git clone
<NAME_EMAIL>:itwanger/paicoding.git
git clone https://github.com/itwanger/paicoding.git
```

**下载release包**

若希望从一个稳定的版本进行尝试，推荐在release页下载zip包，然后本机解压

- [https://github.com/itwanger/paicoding/releases](https://github.com/itwanger/paicoding/releases)

### 2.2 项目导入

以IDEA开发工具为例

- File -> Open
- 选择第一步clone的项目工程

项目导入成功之后，会自动下载依赖、构建索引，此过程用时取决于您的机器性能+网速，通常会持续一段时间，请耐心等待；当完成之后，一个正常的项目工程如下图所示

![](https://cdn.tobebetterjavaer.com/images/20240108/afeef4e1230c423d807e175dc8fbbb2a.png)

如果发现项目 build 未成功或者无法运行 Java 程序，要立马检查一下自己 Intellij IDEA 中的 Maven 是否配置成功。

![](https://cdn.tobebetterjavaer.com/images/20240108/b9af8630c3bb4103bccf4f9c891994cc.png)


### 2.3 配置修改

在正式启动项目之前，还有几个前置步骤需要执行一下

#### 2.3.1 数据库准备

本项目会使用数据库，因此在本机启动时，请先指定数据库；项目中默认的数据库名为 `paicoding`，可以通过修改配置文件中的`database.name`参数来替换为您喜欢的数据库名

数据库名配置: [forum-web/src/main/resources/application.yml](../forum-web/src/main/resources/application.yml)

```yaml
# 默认的数据库名
database:
  name: paicoding
```

本项目中所有使用的表定义放在 [liquibase](../forum-web/src/main/resources/liquibase)

> 本项目提供了自动创建库表的功能，在项目启动之后，当库不存在时，会创建库；当表不存在时，会自动创建表，且会初始化一些测试数据
>
> 因此不建议用户自己通过上面的sql进行创建表

#### 2.3.2 数据库配置

接下来我们需要做的就是设置数据库的相关连接配置

首先在进入之前，先简单了解一下配置，当前所有的配置放在`forum-web`模块内，我们做了环境区分，

![](https://cdn.tobebetterjavaer.com/images/20240108/02c0500aba284f1aaeeb9663d844e020.png)


- dev: 本地开发环境
- test: 测试环境
- pre: 预发环境
- prod: 生产环境

默认的环境选择是`dev`，可以通过下面两种方式进行环境切换

**case1: 命令切换**

```bash
# 切换到test环境
mvn clean package -DskipTests=true -Ptest
```

**case2: idea切换**

![](https://cdn.tobebetterjavaer.com/images/20240108/ddd4af8aaf34448fa20ddb11789a94f7.png)


接下来以默认的dev环境配置为例，首先进入配置文件 [application-dal.yml](../forum-web/src/main/resources-env/dev/application-dal.yml)

```yaml
spring:
  datasource:
    # 数据库名，从配置 database.name 中获取
    url: ***************************/${database.name}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai
    username: root
    password:
```

上面的数据库中，通常来讲需要修改的有三个

- url: 主要修改的就是这个数据库的域名 + 端口号，即将`127.0.0.1:3306`替换为您实际使用的数据库地址
- username: 数据库名
- password: 数据库密码

#### 2.3.3 文件上传配置

暂时省略，后续补齐

### 2.4 启动

接下来就可以直接启动项目了

进入启动类: `QuickForumApplication`

![](https://cdn.tobebetterjavaer.com/images/20240108/6238d17c93d640deb0e1123d02fa270d.png)


启动完毕之后，将会在控制台看到如下输出

![](https://cdn.tobebetterjavaer.com/images/20240108/3fddd6712f0846879b445d378813a15f.png)


点击控制台中的链接进入首页, 默认首页为: [http://127.0.0.1:8080](http://127.0.0.1:8080)

然后就可以开始愉快的玩耍了，对了，记得启动 Redis。

![](https://cdn.tobebetterjavaer.com/images/20240108/9b1242b0ef6a4aec83b1951e5dd5b3b6.png)
