/*

    Name:       3024 night
    Author:     <PERSON> (http://github.com/idleberg)

    CodeMirror template by <PERSON> (https://github.com/idleberg/base16-codemirror)
    Original Base16 color scheme by <PERSON> (https://github.com/chriske<PERSON>on/base16)

*/

.cm-s-3024-night.CodeMirror {background: #090300; color: #d6d5d4;}
.cm-s-3024-night div.CodeMirror-selected {background: #3a3432 !important;}
.cm-s-3024-night.CodeMirror ::selection { background: rgba(58, 52, 50, .99); }
.cm-s-3024-night.CodeMirror ::-moz-selection { background: rgba(58, 52, 50, .99); }
.cm-s-3024-night .CodeMirror-gutters {background: #090300; border-right: 0px;}
.cm-s-3024-night .CodeMirror-guttermarker { color: #db2d20; }
.cm-s-3024-night .CodeMirror-guttermarker-subtle { color: #5c5855; }
.cm-s-3024-night .CodeMirror-linenumber {color: #5c5855;}

.cm-s-3024-night .CodeMirror-cursor {border-left: 1px solid #807d7c !important;}

.cm-s-3024-night span.cm-comment {color: #cdab53;}
.cm-s-3024-night span.cm-atom {color: #a16a94;}
.cm-s-3024-night span.cm-number {color: #a16a94;}

.cm-s-3024-night span.cm-property, .cm-s-3024-night span.cm-attribute {color: #01a252;}
.cm-s-3024-night span.cm-keyword {color: #db2d20;}
.cm-s-3024-night span.cm-string {color: #fded02;}

.cm-s-3024-night span.cm-variable {color: #01a252;}
.cm-s-3024-night span.cm-variable-2 {color: #01a0e4;}
.cm-s-3024-night span.cm-def {color: #e8bbd0;}
.cm-s-3024-night span.cm-bracket {color: #d6d5d4;}
.cm-s-3024-night span.cm-tag {color: #db2d20;}
.cm-s-3024-night span.cm-link {color: #a16a94;}
.cm-s-3024-night span.cm-error {background: #db2d20; color: #807d7c;}

.cm-s-3024-night .CodeMirror-activeline-background {background: #2F2F2F !important;}
.cm-s-3024-night .CodeMirror-matchingbracket { text-decoration: underline; color: white !important;}
