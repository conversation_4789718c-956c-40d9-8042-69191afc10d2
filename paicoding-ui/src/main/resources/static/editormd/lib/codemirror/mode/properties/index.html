<!doctype html>

<title>CodeMirror: Properties files mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="properties.js"></script>
<style>.CodeMirror {border-top: 1px solid #ddd; border-bottom: 1px solid #ddd;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Properties files</a>
  </ul>
</div>

<article>
<h2>Properties files mode</h2>
<form><textarea id="code" name="code">
# This is a properties file
a.key = A value
another.key = http://example.com
! Exclamation mark as comment
but.not=Within ! A value # indeed
   # Spaces at the beginning of a line
   spaces.before.key=value
backslash=Used for multi\
          line entries,\
          that's convenient.
# Unicode sequences
unicode.key=This is \u0020 Unicode
no.multiline=here
# Colons
colons : can be used too
# Spaces
spaces\ in\ keys=Not very common...
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {});
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-properties</code>,
    <code>text/x-ini</code>.</p>

  </article>
