<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="utf-8" />
        <title>On / Off (bind/unbind) event handle - Editor.md examples</title>
        <link rel="stylesheet" href="css/style.css" />
        <link rel="stylesheet" href="../css/editormd.css" />
        <link rel="shortcut icon" href="https://pandao.github.io/editor.md/favicon.ico" type="image/x-icon" />
    </head>
    <body>
        <div id="layout">
            <header>
                <h1>On / Off (bind/unbind) event handle</h1>
                <p>Plaese press F12, open the develop tools.</p>     
            </header>
            <div id="test-editormd">                
                <textarea style="display:none;">#### Example
```javascript
{
    onscroll : function(event) {
        console.clear();
        console.log("onscroll =>", this, this.id, this.settings, event);
    },

    onpreviewscroll : function(event) {
        console.clear();
        console.log("onpreviewscroll =>", this, this.id, this.settings, event);
    },

    onload : function() {
        this.off("previewscroll"); // unbind before handle

        // Override settings.onpreviewscroll
        this.on("previewscroll", function(){
            console.clear();
            console.log("on() => Override settings.onpreviewscroll =>", this, this.id, event, (new Date).getTime());
        });

        // defined event bind
        this.on("resize", function(){
            console.clear();
            console.log("onresize =>", this, this.id, event, (new Date).getTime());
        });
    }
}

// Or
editor.on("resize", function(){
    // ...
});

editor.off("resize");
```

</textarea>
            </div>
        </div>        
        <script src="js/jquery.min.js"></script>
        <script src="../editormd.js"></script>
        <script type="text/javascript">
            var testEditor;
            
            $(function() {
                $.get("./test.md", function(md){
                    testEditor = editormd("test-editormd", {
                        width    : "90%",
                        height   : 720,
                        appendMarkdown : md,
                        path     : '../lib/',
                        tex      : true,
                        htmlDecode : true,
                        flowChart : true,
                        taskList  : true,
                        sequenceDiagram : true,
                        onscroll : function(event) {
                            console.clear();
                            console.log("onscroll =>", this, this.id, this.settings, event);
                        },
                        onpreviewscroll : function(event) {
                            console.clear();
                            console.log("onpreviewscroll =>", this, this.id, this.settings, event);
                        },
                        onload : function() {
                            this.off("previewscroll");
                            
                            // Override settings.onpreviewscroll
                            this.on("previewscroll", function(){
                                console.clear();
                                console.log("on() => Override settings.onpreviewscroll =>", this, this.id, event, (new Date).getTime());
                            });
                            
                            // defined event bind
                            this.on("resize", function(){
                                console.clear();
                                console.log("onresize =>", this, this.id, event, (new Date).getTime());
                            });
                        }
                    });
                });
            });
        </script>
    </body>
</html>