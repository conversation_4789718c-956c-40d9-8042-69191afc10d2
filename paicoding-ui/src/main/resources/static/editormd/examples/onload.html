<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="utf-8" />
        <title>Onload - Editor.md examples</title>
        <link rel="stylesheet" href="css/style.css" />
        <link rel="stylesheet" href="../css/editormd.css" />
        <link rel="shortcut icon" href="https://pandao.github.io/editor.md/favicon.ico" type="image/x-icon" />
    </head>
    <body>
        <div id="layout">
            <header>
                <h1>Onload event</h1>   
                <p>Plaese press F12, open the develop tools.</p>     
            </header>
            <div id="test-editormd">                
                <textarea style="display:none;">#### Setting

```javascript
{
    onload : function() {
        // alert("onload");
        // this.setMarkdown("### onloaded");
        // console.log("onload =>", this, this.id, this.settings);
    }
}
```
</textarea>
            </div>
        </div>        
        <script src="js/jquery.min.js"></script>
        <script src="../editormd.js"></script>
        <script type="text/javascript">
            $(function() {
                var testEditor = editormd("test-editormd", {
                    width  : "90%",
                    height : 720,
                    //watch : false,
                    //matchWordHighlight : "onselected",
                    path   : '../lib/',
                    onload : function() {
                        //this.watch();
                        //this.setMarkdown("###test onloaded");
                        //testEditor.setMarkdown("###Test onloaded");
                        alert("onloaded");
                        console.log("onloaded =>", this, this.id, this.settings);
                    }
                });
            });
        </script>
    </body>
</html>