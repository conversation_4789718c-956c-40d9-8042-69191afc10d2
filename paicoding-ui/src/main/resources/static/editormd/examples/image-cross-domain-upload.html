<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="utf-8" />
        <title>图片跨域上传示例 - Editor.md examples</title>
        <link rel="stylesheet" href="css/style.css" />
        <link rel="stylesheet" href="../css/editormd.css" />
        <link rel="shortcut icon" href="https://pandao.github.io/editor.md/favicon.ico" type="image/x-icon" />
    </head>
    <body>
        <div id="layout" style="height: 2000px;background: #f6f6f6;">
            <header>
                <h1>图片跨域上传示例</h1>
                <p>Image cross-domain upload example.</p>
            </header>
            <div id="test-editormd">
                <textarea style="display:none;">#### Settings

```javascript
{
    imageUpload       : true,
    imageFormats      : ["jpg", "jpeg", "gif", "png", "bmp", "webp"],
    imageUploadURL    : "http://xxxxxxx/editor.md/examples/php/cross-domain-upload.php?test=dfdf",
    crossDomainUpload : true,
    uploadCallbackURL : "http://xxxxxx/upload_callback.html?test=dfdf"
}
```

#### 跨域上传原理

利用 iframe 来实现，A 站 POST 到 B 站，B 站处理上传后再跳转回到 A 站的 callback 页面，callback 页面此时在 iframe 内（即同域下），再通过 window.parent 就可以操作父页面的任意元素了。

#### 跨域上传示例 PHP 版

当前域名：blog.xxx.com/post.php

```html
&lt;form method="post" target="upload-iframe" enctype="multipart/form-data" action="http://static.xxx.com/uploader.php?callback=http://blog.xxx.com/upload-callback.html"&gt;
    &lt;input type="file" name="file" accept="image/*" /&gt;
    &lt;input type="submit" id="submit" value="开始上传" /&gt;
&lt;/form&gt;
&lt;iframe name="upload-iframe" style="display:none;" frameborder="0"&gt;&lt;/iframe&gt;  
```     

图片服务器：static.xxx.com/uploader.php

```php
&lt;?php
header("Access-Control-Allow-Origin: *"); // Setting allow domian name

$file = 'uploads/' . $_FILES['file']['name'];
// 详细过程略
move_uploaded_file($_FILES['file']['tmp_name'], $file);

$location = $_GET['callback'].'?success=1&message=xxxxxxx&url=http://static.xxx.com/2015/02/15'.$file.'&temp='.date('ymdhis');

header('location:' . $location);
exit;
?&gt;
```

当前域名：blog.xxx.com/upload-callback.html

```html
&lt;script type="text/javascript"&gt; 
    var query = {};
    var urlParams = window.location.search.split('?')[1];
    urlParams = urlParams.split("&");

    for (var i = 0; i&lt; urlParams.length; i++) {
        var param = urlParams[i].split("="); 
        query[param[0]] = param[1]; 
    }

    var imageDialog = window.parent.document.getElementById(query['dialog_id']);
    //console.log(imageDialog, window.parent.document, window.parent, query);
&lt;/script&gt;
```</textarea>
            </div>
        </div>        
        <script src="js/jquery.min.js"></script>
        <script src="../editormd.js"></script>
        <script type="text/javascript">
            $(function() {
                var testEditor = editormd("test-editormd", {
                    width             : "90%",
                    height            : 640,
                    markdown          : "",
                    path              : '../lib/',
                    imageUpload       : true,
                    imageFormats      : ["jpg", "jpeg", "gif", "png", "bmp", "webp"],
                    imageUploadURL    : "http://www.ipandao.com/editor.md/examples/php/cross-domain-upload.php?test=dfdf",
                    crossDomainUpload : true,
                    uploadCallbackURL : "http://localhost/editor.md/examples/php/upload_callback.html?test=dfdf"
                    
                    /*
                     跨域时，上传的图片服务器后台只需要返回一个跳转 URL 并跳转到原页面同域下的 callback 页面，结构如下：
                     {
                        success   : 0 | 1,   // 0 表示上传失败，1 表示上传成功
                        message   : "提示的信息，上传成功或上传失败及错误信息等。",
                        dialog_id : $_GET['dialog_id'],
                        url       : "远程图片地址"    // 上传成功时才返回
                     }
                     */
                });
            });
        </script>
    </body>
</html>