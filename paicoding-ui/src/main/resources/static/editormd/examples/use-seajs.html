<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="utf-8" />
        <title>Using sea.js - Editor.md examples</title>
        <link rel="stylesheet" href="css/style.css" />
        <link rel="stylesheet" href="../css/editormd.min.css" /> 
        <link rel="shortcut icon" href="https://pandao.github.io/editor.md/favicon.ico" type="image/x-icon" />
        <style>
            header p code {
                padding: 3px;
                border: 1px solid #ddd;
                background: #f6f6f6;
                border-radius: 3px;
                font-size: 85%;
                font-family: Consolas, 'Monaco', courier, monospace;
            }
        </style>
    </head>
    <body>
        <div id="layout">
            <header>
                <h1>Using sea.js</h1>   
                <p>说明：</p>
                <p>1、由于Editor.md是依赖jQuery的，所以在使用Sea.js加载Editor.md时，要在jQuery.js文件尾部加上 <code>if(typeof define === "function") { define(function () { return $.noConflict(); }); }</code>，使jQuery支持Sea.js；</p>  
                <p>2、flowChart（流程图）和sequenceDiagram（时序图）基于Raphael.js，由于疑似Sea.js与Raphael.js有冲突，所以必须先加载Raphael.js再加载Sea.js，Editor.md才能在Sea.js下正常进行；</p>      
                <ul style="margin: 10px 0 0 18px;">
                    <li>Enable HTML tags decode</li>
                    <li>Enable TeX, Flowchart, Sequence Diagram, Emoji, FontAwesome, Task lists</li>
                    <li>Enable Image upload</li>
                    <li>Enable [TOCM], Search Replace, Code fold</li>
                </ul>  
            </header>
            <div class="btns">
                <button id="show-btn">Show editor</button>
                <button id="hide-btn">Hide editor</button>
                <button id="get-md-btn">Get Markdown</button>
                <button id="get-html-btn">Get HTML</button>
                <button id="watch-btn">Watch</button>
                <button id="unwatch-btn">Unwatch</button>
                <button id="preview-btn">Preview HTML (Press Shift + ESC cancel)</button>
                <button id="fullscreen-btn">Fullscreen (Press ESC cancel)</button>
                <button id="show-toolbar-btn">Show toolbar</button>
                <button id="close-toolbar-btn">Hide toolbar</button>
                <button id="toc-menu-btn">ToC Dropdown menu</button>
                <button id="toc-default-btn">ToC default</button>
            </div>
            <div id="test-editormd"></div>
        </div>   
        <!-- Flowchart & Sequence Diagram based on Raphael.js, and must load Raphael.js of Sea.js before. -->
        <script src="../lib/raphael.min.js"></script>
        <script src="js/sea.js"></script>
        <script type="text/javascript">
            seajs.config({
                base  : "./",
                alias : {
                    jquery   : "js/jquery.min",
                    editormd : "../editormd"
                }
            });
                
            //seajs.use("./js/seajs-main"); //使用main.js时 editormd 路径要改为 "../../editormd"
            
            var deps = [
                "jquery", 
                "editormd", 
                "../languages/en", 
                "../plugins/link-dialog/link-dialog",
                "../plugins/reference-link-dialog/reference-link-dialog",
                "../plugins/image-dialog/image-dialog",
                "../plugins/code-block-dialog/code-block-dialog",
                "../plugins/table-dialog/table-dialog",
                "../plugins/emoji-dialog/emoji-dialog",
                "../plugins/goto-line-dialog/goto-line-dialog",
                "../plugins/help-dialog/help-dialog",
                "../plugins/html-entities-dialog/html-entities-dialog", 
                "../plugins/preformatted-text-dialog/preformatted-text-dialog"
            ];
			
            seajs.use(deps, function($, editormd) {
                var testEditor;
                
                $.get("./test.md", function(md){
                    testEditor = editormd("test-editormd", {
                        width: "90%",
                        height: 640,
                        path : '../lib/',
                        markdown : md,
                        //toolbar  : false,             // 关闭工具栏
                        codeFold : true,
                        searchReplace : true,
                        saveHTMLToTextarea : true,      // 保存 HTML 到 Textarea
                        htmlDecode : "style,script,iframe|on*",            // 开启 HTML 标签解析，为了安全性，默认不开启
                        emoji : true,
                        taskList : true,
                        tocm            : true,          // Using [TOCM]
                        tex : true,                      // 开启科学公式 TeX 语言支持，默认关闭
                        //previewCodeHighlight : false,  // 关闭预览窗口的代码高亮，默认开启
                        flowChart : true,                // 疑似 Sea.js与 Raphael.js 有冲突，必须先加载 Raphael.js ，Editor.md 才能在 Sea.js 下正常进行；
                        sequenceDiagram : true,          // 同上
                        //dialogLockScreen : false,      // 设置弹出层对话框不锁屏，全局通用，默认为 true
                        //dialogShowMask : false,     // 设置弹出层对话框显示透明遮罩层，全局通用，默认为 true
                        //dialogDraggable : false,    // 设置弹出层对话框不可拖动，全局通用，默认为 true
                        //dialogMaskOpacity : 0.4,    // 设置透明遮罩层的透明度，全局通用，默认值为 0.1
                        //dialogMaskBgColor : "#000", // 设置透明遮罩层的背景颜色，全局通用，默认为 #fff
                        imageUpload : true,
                        imageFormats : ["jpg", "jpeg", "gif", "png", "bmp", "webp"],
                        imageUploadURL : "./php/upload.php",
                        onload : function() {
                            console.log('onload', this);
                            //this.fullscreen();
                            //this.unwatch();
                            //this.watch().fullscreen();

                            //this.setMarkdown("#PHP");
                            //this.width("100%");
                            //this.height(480);
                            //this.resize("100%", 640);
                        }
                    });
                });

                $("#show-btn").bind('click', function(){
                    testEditor.show();
                });

                $("#hide-btn").bind('click', function(){
                    testEditor.hide();
                });

                $("#get-md-btn").bind('click', function(){
                    alert(testEditor.getMarkdown());
                });

                $("#get-html-btn").bind('click', function() {
                    alert(testEditor.getHTML());
                });                

                $("#watch-btn").bind('click', function() {
                    testEditor.watch();
                });                 

                $("#unwatch-btn").bind('click', function() {
                    testEditor.unwatch();
                });              

                $("#preview-btn").bind('click', function() {
                    testEditor.previewing();
                });

                $("#fullscreen-btn").bind('click', function() {
                    testEditor.fullscreen();
                });
                
                $("#show-toolbar-btn").bind('click', function() {
                    testEditor.showToolbar();
                });
                
                $("#close-toolbar-btn").bind('click', function() {
                    testEditor.hideToolbar();
                });
                
                $("#toc-menu-btn").click(function(){
                    testEditor.config({
                        tocDropdown   : true,
                        tocTitle      : "目录 Table of Contents",
                    });
                });
                
                $("#toc-default-btn").click(function() {
                    testEditor.config("tocDropdown", false);
                });
            });
        </script>
    </body>
</html>