!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}("undefined"!=typeof self?self:this,(function(){return function(){"use strict";var e={},t={inline:["$","$"],display:["$$","$$"]};var n=function(e,n){void 0===n&&(n=t);for(var r=e.querySelectorAll(".katex-mathml + .katex-html"),a=0;a<r.length;a++){var o=r[a];o.remove?o.remove():o.parentNode&&o.parentNode.removeChild(o)}for(var i=e.querySelectorAll(".katex-mathml"),l=0;l<i.length;l++){var f=i[l],c=f.querySelector("annotation");c&&(f.replaceWith?f.replaceWith(c):f.parentNode&&f.parentNode.replaceChild(c,f),c.innerHTML=n.inline[0]+c.innerHTML+n.inline[1])}for(var d=e.querySelectorAll(".katex-display annotation"),s=0;s<d.length;s++){var p=d[s];p.innerHTML=n.display[0]+p.innerHTML.substr(n.inline[0].length,p.innerHTML.length-n.inline[0].length-n.inline[1].length)+n.display[1]}return e};function r(e){var t=e instanceof Element?e:e.parentElement;return t&&t.closest(".katex")}return document.addEventListener("copy",(function(e){var t=window.getSelection();if(!t.isCollapsed&&e.clipboardData){var a=e.clipboardData,o=t.getRangeAt(0),i=r(o.startContainer);i&&o.setStartBefore(i);var l=r(o.endContainer);l&&o.setEndAfter(l);var f=o.cloneContents();if(f.querySelector(".katex-mathml")){var c=Array.prototype.map.call(f.childNodes,(function(e){return e instanceof Text?e.textContent:e.outerHTML})).join("");a.setData("text/html",c),a.setData("text/plain",n(f).textContent),e.preventDefault()}}})),e=e.default}()}));