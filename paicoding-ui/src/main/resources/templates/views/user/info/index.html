<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
  <div class="user-bg" th:fragment="user_info(userHome)">
    <div class="user-head">
      <img th:src="${vo.userHome.photo}" class="user-head-img" />
      <div class="user-head-title-wrap">
        <div class="user-head-title-name" th:text="${userHome.userName}">
          用户名用户名用户名用户名用户名用户名用户名用户名用户名用户名用户名用户名
        </div>
        <div class="user-head-title-classify">
          <div class="user-head-title-classify-item">
            <span class="text-base-pure">加入天数</span>
            <span th:text="${userHome.joinDayCount}">1</span>
          </div>
          <div class="user-head-cell"></div>
          <div class="user-head-title-classify-item">
            <span class="text-base-pure">关注数</span>
            <span th:text="${userHome.followCount}">1</span>
          </div>
          <div class="user-head-cell"></div>
          <div class="user-head-title-classify-item">
            <span class="text-base-pure">粉丝数</span>
            <span th:text="${userHome.fansCount}">1</span>
          </div>
        </div>
      </div>
      <div class="user-head-footer">
        <div class="tw-flex-1">
          <div class="d-flex tags">
            <div class="tag-item" th:if="${!#strings.isEmpty(userHome.company)}">
              <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAAAsklEQVR42mNgoBPI507vTbuU/g0OP6adSM+rZ8KpPO162n9MmL4Wh4b0XmzKwTAKqwbs5oPtWIJdA0Ryb4ZKmhwEZuql3QGLnsCivJ4J5uIajnomCCwUSrsEFn2cXpVelZGYoYJFA36Y0UqihrT/GYkkaki7hKEhfW/6DghMO4FNCzQi4Z7uRYmbvZgaajhQbbiTnpeeAYVV6R8JayAIR7KG9M3EakjfTJ4GiLMIQzKLCwBaavBCKUO/owAAAABJRU5ErkJggg=="
                   class="tw-h-3 tw-w-3 tw-mr-1">
              <span class="tw-text-sm" th:text="${userHome.company}">第一拖拉机制造厂拖拉机学院</span>
            </div>
            <div class="tag-item" th:if="${!#strings.isEmpty(userHome.position)}">
              <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAABIUlEQVR42r1TLW/DQAw1OxgWUhypNCykZDS0P6GyLzmVHxzobxgpLAjt2FBJf0DR4UiVCkMLQjpdmt1XsiZkc1Dk92w/3zPAvwWleKIWH8OPWlT8LYAXEV3HwIbUbBceAcUreEfZ+QQ1RcCbO9BqEq575FbwYQ4Bjz1cxP12VJlgNah7KJPnwNSK+Dn/pk8qySSjr7CqZEah6Aj83VbTC8aLgZ8kA7Bd+01R6i9PxFh3f5ciAsAPJ5v+iN45QwiAMqEGaz2x7R68BO2tEWgNwLMy8Z+T9oE58NOk7jzriqwdbx0H5pOMztY5xcqF01mLH0QR/WIQpcWPxnYx4tk68KkffEmNb2u+nDgkntE9lD91e7lZcD7zXHmGFVazqv9BfAN523/xHVmeOQAAAABJRU5ErkJggg=="
                   class="tw-h-3 tw-w-3 tw-mr-1">
              <span class="tw-text-sm" th:text="${userHome.position}">Java</span>
            </div>
            <div class="tag-item" th:if="${!#strings.isEmpty(userHome.region)}">
              <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAQAAABKfvVzAAABFUlEQVR42mNgoBRkSaRnpC1PP5R+KG15ekaWBH7FPOnd6T/S/iNg+o/07iweXMpl0y4iK4ZrupAli0V5GW/6VWzKwVqulvFiaEibi0s5GM5Fd45m+l98GtL/ZmmiaMhoRZL+mV6eJpkjlV6e9hMhmtGK6qAzSKaVw0TTy5GMOYOq4TWSlCRcVBJJ9DWqhj/YNORIIYn+QdXwiqCTUG1I34/u6TRJVE+nHUbVUIk3FkCh1ICioVAo7SteDb8QPsMWE5gRNwUjaeSyp13DqeExlrTEwJBhiN1Z6b8zHHAk8LTQtH9YNGThyUJpBRjKmwlk0YwyFA3tROTq9AxoUv+XVkxkQZDum/Yp7VNaAAllR6FQpiB2GQBLang0fXCP5wAAAABJRU5ErkJggg=="
                   class="tw-h-3 tw-w-3 tw-mr-1">
              <span class="tw-text-sm" th:text="'IP属地：' + ${userHome.region}">IP属地：河南</span>
            </div>
          </div>
          <div class="user-text">
            <span th:text="${#strings.isEmpty(userHome.profile) ? '点击添加简介，让大家认识你吧': userHome.profile}">点击添加简介，让大家认识你</span>

            <span class="user-text-icon"
                  data-toggle="modal"
                  data-target="#saveModel" th:if="${global.isLogin && vo.userHome.userId == global.user.userId}">
                <svg
                        t="1670060220524"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="4260"
                        width="20"
                        height="20"
                >
                  <path
                          d="M912.5 797.8H183.4c-15.1 0-27.4-12.3-27.4-27.4 0-15.1 12.3-27.4 27.4-27.4h729.1c15.1 0 27.4 12.3 27.4 27.4 0.1 15.1-12.3 27.4-27.4 27.4zM494.9 588.5l-38-32.2 281.9-332c7.8-9.2 8.3-22 1.2-28.1l-56.3-47.9c-3.9-3.4-8.9-3.6-12.1-3.1-5.6 0.8-11.3 4-15.4 8.9L374.4 486l-38-32.2 281.9-332c12.1-14.3 28.7-23.5 46.6-25.9 18.8-2.5 37.1 2.5 51.2 14.5l56.3 47.9c28.2 24 30.2 68.1 4.5 98.3l-282 331.9z"
                          fill="#828282"
                          p-id="4261"
                  ></path>
                  <path
                          d="M341.2 656.6c-10.5 0-20.4-3.6-28.4-10.3-13-11-18.5-28.5-14.4-45.7l38.9-147.5 48.2 12.8-36.1 136.9 124.9-60.5 21.7 44.9-134.9 65.2c-6.6 2.8-13.3 4.2-19.9 4.2z"
                          fill="#828282"
                          p-id="4262"
                  ></path>
                  <path
                          d="M339.418 456.257l30.058-35.348 152.436 129.625-30.058 35.348zM589.299 191.09l30.447-35.804 129.2 109.867-30.446 35.805z"
                          fill="#828282"
                          p-id="4263"
                  ></path>
                </svg>
              </span>
          </div>
        </div>

        <div class="user-edit" th:if="${global.isLogin && vo.userHome.userId == global.user.userId}">
          <span th:text="'个人资料完善度：' + ${vo.userHome.infoPercent} + '%'">个人资料完善度：70%</span>
          <span class="edit-btn" data-toggle="modal" data-target="#saveModel">去编辑 ></span>
        </div>
      </div>
    </div>
    <div class="user-bg-mask"></div>
  </div>
</html>
