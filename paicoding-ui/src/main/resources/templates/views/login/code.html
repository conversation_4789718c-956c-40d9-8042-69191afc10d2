<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<body>
<div th:replace="components/layout/header :: head(~{::title}, ~{}, ~{})">
    <title th:text="${global.siteInfo.websiteName}">技术派</title>
</div>

<!-- 导航栏 -->
<div th:replace="components/layout/container :: container_all(~{::content})">
    <content>
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loginModalDropLabel">二维码登录</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <img src="//cdn.tobebetterjavaer.com/paicoding/a768cfc54f59d4a056f79d1c959dcae9.jpg" style="max-width: 400px;">
                    <small>关注微信公众号，输入 “登录” or "login“ 关键词，将返回的验证码填在下面的输入框，点击登录即可</small>
                </div>
                <br/>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">验证码</span>
                    </div>
                    <input id="loginCode" type="number" class="form-control" placeholder="请输入登录验证码"/>
                </div>
            </div>
            <div class="modal-footer">
                <button id="loginBtn" type="button" class="btn btn-primary">登录</button>
                <!-- 非生产环境，使用模拟登陆  -->
                <button th:if="${!#strings.equals(global.env, 'prod')}" id="getToken" type="button"
                        class="btn btn-danger">获取token(测试用)
                </button>
            </div>
            <div id="testOutput"></div>
        </div>
    </content>
</div>

<div th:replace="components/layout/footer :: footer"></div>
</body>
</html>