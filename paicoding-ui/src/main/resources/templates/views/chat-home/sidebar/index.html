<div class="home_sidebar-header__b5asC">
    <div class="home_sidebar-title__d8_c_">
        <a href="https://paicoding.com/article/detail/358" class="underline">戳我了解如何使用派聪明</a>
    </div>
    <div class="home_sidebar-sub-title__IS2Or">
        <input type="text" id="promptField" placeholder="请输入提示词，回车确认" style="width: 100%; height: 30px; padding: 5px;margin-bottom: 1em">
        <span style="margin-top:1em;">
            <svg class="icon"
                 style="margin-right:0.3em;width: 1em;height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;"
                 viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                 p-id="3609"><path
                    d="M512 993.882353C245.850353 993.882353 30.117647 778.149647 30.117647 512S245.850353 30.117647 512 30.117647s481.882353 215.732706 481.882353 481.882353-215.732706 481.882353-481.882353 481.882353z m0-60.235294c232.869647 0 421.647059-188.777412 421.647059-421.647059S744.869647 90.352941 512 90.352941 90.352941 279.130353 90.352941 512s188.777412 421.647059 421.647059 421.647059z"
                    fill="#000000" p-id="3610"></path><path
                    d="M459.956706 763.572706c-5.12-34.665412-1.204706-105.893647 0-120.530824l5.300706-165.49647c0.963765-15.36-7.981176-11.444706-14.125177-8.553412-29.816471 14.215529-64.602353 40.357647-64.602353 40.357647s21.172706-57.283765 41.592471-81.227294c15.088941-21.052235 47.465412-49.182118 71.258353-51.410824 28.039529 0.602353 42.405647 4.848941 40.05647 74.842353 0 0-4.698353 215.100235-0.662588 252.596706 4.096 11.956706 43.339294-15.209412 48.850824-19.516235 5.541647-4.306824 56.32-50.176 56.32-50.176S602.352941 759.868235 522.300235 787.787294c-28.521412 9.938824-58.066824 5.150118-62.373647-24.214588z"
                    fill="#000000" opacity=".99" p-id="3611"></path><path
                    d="M503.717647 285.364706m-52.495059 0a52.495059 52.495059 0 1 0 104.990118 0 52.495059 52.495059 0 1 0-104.990118 0Z"
                    fill="#000000" p-id="3612"></path><path d="M0 0h1024v1024H0z" fill="#FFF4F4" fill-opacity="0"
                                                            p-id="3613"></path></svg>提示词：对AI进行角色定位，一个简单的示例如： <div
                style="font-weight: bold;color: blue">你现在扮演李白，你豪情万丈，狂放不羁；接下来请用李白的口吻和用户对话。</div></span>
    </div>
    <div class="home_sidebar-logo__FFdBS no-dark">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="43" height="44"
             fill="none">
            <defs>
                <path id="chatgpt_svg__a" d="M0 0h43v43.58H0z"></path>
            </defs>
            <g>
                <mask id="chatgpt_svg__b" fill="#fff">
                    <use xlink:href="#chatgpt_svg__a"></use>
                </mask>
                <g mask="url(#chatgpt_svg__b)">
                    <path fill-rule="evenodd" opacity="0.27"
                          d="M40.17 17.84c.36-1.11.55-2.27.55-3.43 0-1.93-.51-3.83-1.49-5.49a10.98 10.98 0 0 0-9.52-5.51c-.77 0-1.55.08-2.3.24A10.868 10.868 0 0 0 19.29 0h-.1c-4.76 0-8.98 3.07-10.45 7.6-3.06.63-5.71 2.55-7.26 5.27a10.993 10.993 0 0 0 1.35 12.87c-.36 1.11-.55 2.27-.55 3.43 0 1.93.51 3.83 1.49 5.49a10.97 10.97 0 0 0 11.82 5.27c2.06 2.32 5.02 3.65 8.12 3.65h.1c4.76 0 8.99-3.07 10.45-7.61a10.82 10.82 0 0 0 7.26-5.26 10.995 10.995 0 0 0-1.35-12.87ZM18.817 38.695c-.09.05-.17.1-.26.15a8.145 8.145 0 0 0 5.22 1.89h.01c4.5-.01 8.15-3.67 8.16-8.17v-10.13a.153.153 0 0 0-.07-.1l-3.67-2.12v12.24c0 .51-.27.98-.72 1.23l-8.67 5.01Zm-1.424-2.472 8.77-5.06c.04-.03.06-.07.06-.11h-.01v-4.24l-10.59 6.12c-.44.25-.98.25-1.42 0l-8.68-5.01c-.08-.05-.2-.12-.26-.16a8.19 8.19 0 0 0 .97 5.47 8.18 8.18 0 0 0 7.08 4.08c1.43 0 2.84-.37 4.08-1.09Zm-9.187-25.21v-.3c-1.79.66-3.3 1.93-4.25 3.58a8.226 8.226 0 0 0-1.09 4.08c0 2.92 1.55 5.61 4.08 7.07l8.77 5.07c.04.02.09.02.12-.01l3.67-2.12-10.59-6.11c-.44-.25-.71-.72-.71-1.23v-10.03Zm27.849 7.117-8.77-5.07a.126.126 0 0 0-.12.01l-3.67 2.12 10.59 6.12c.44.25.71.71.71 1.22v10.33a8.168 8.168 0 0 0 5.35-7.66 8.16 8.16 0 0 0-4.09-7.07Zm-19.22-5.718a.16.16 0 0 0-.05.11v4.24l10.59-6.12c.22-.12.47-.19.72-.19s.49.07.71.19l8.68 5.02c.08.05.17.1.25.15.08-.46.12-.92.12-1.38 0-4.51-3.66-8.17-8.17-8.17-1.43 0-2.83.38-4.08 1.09l-8.77 5.06ZM19.22 2.85c-4.51 0-8.17 3.65-8.17 8.16v10.13c.01.05.03.08.07.1l3.67 2.12.01-12.23v-.01c0-.5.27-.97.71-1.22l8.68-5.01c.07-.05.19-.11.25-.15a8.145 8.145 0 0 0-5.22-1.89ZM16.783 24.51l4.72 2.73 4.72-2.73v-5.45l-4.72-2.72-4.72 2.73v5.44Z"
                          style="fill: var(--pai-brand-1-normal);"></path>
                </g>
            </g>
        </svg>
    </div>

</div>

<style>
    .new-chat {
        align-items: center;
        height: 100%;
        padding: 4px 10px;
        display: flex;
        cursor: pointer;
        color: var(--pai-brand-1-normal);
        background-color: var(--pai-brand-7-light);
        border-radius: 14px;
        flex-shrink: 0;
        width: fit-content;
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
    }

    .history-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .history-list li {
        position: relative;
        height: 38px;
        font-size: 14px;
        padding: 0 10px;
        cursor: pointer;
        border-radius: 12px;
        margin-bottom: 5px;
        background-color: #e9e9e9;
        display: flex;
        justify-content: space-between;
        align-items: center;

    }

    /* 新增样式 */
    .history-list li .chat-title {
        white-space: nowrap;  /* 防止文本换行 */
        overflow: hidden;     /* 隐藏溢出的文本 */
        text-overflow: ellipsis; /* 显示省略号 */
        flex: 1;              /* 占满剩余空间 */
    }

    .history-list li:hover {
        background-color: #ddd;
    }

    .history-list li.active {
        background-color: var(--pai-brand-7-light);
    }

    .history-list li.active .menu-button {
        display: block;
    }

    .menu-button {
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        top: 50%;
        right: 10px;
        border-radius: 8px;
        display: block;
    }

    .menu-button:hover {
        background: white;
    }

    .history-list li:hover .menu-button {
        display: block;
    }

    .menu-popup {
        position: absolute;
        right: 0;
        bottom: -60px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        z-index: 10;
        color: black;
    }

    .menu-item {
        padding: 8px 16px;
        cursor: pointer;
    }

    .menu-popup .delete {
        color: red;
    }

    .menu-item:hover {
        background-color: #f5f5f5;
    }

    /* 自定义确认弹窗样式 */
    .confirm-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        z-index: 20;
        padding: 20px;
        text-align: center;
    }

    .confirm-popup-buttons {
        margin-top: 20px;
    }

    .confirm-popup-buttons button {
        padding: 8px 16px;
        margin: 0 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .confirm-popup-buttons button.confirm-yes {
        background-color: #dc3545;
        color: #fff;
    }

    .confirm-popup-buttons button.confirm-yes:hover {
        background-color: #c82333;
    }

    .confirm-popup-buttons button.confirm-no {
        background-color: #007bff;
        color: #fff;
    }

    .confirm-popup-buttons button.confirm-no:hover {
        background-color: #0056b3;
    }

</style>

<div class="home_sidebar-header__b5asC">
    <div class="home_sidebar-title__d8_c_">
        <div class="underline">聊天历史</div>
    </div>
    <div class="home_sidebar-sub-title__IS2Or">
        <div class="new-chat" id="newChatBtn">
            <div style="margin-right: 9px;">
                <svg width="20" height="20" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.10999 27C8.92999 27 8.76001 26.96 8.60001 26.9C8.43001 26.83 8.29 26.74 8.16 26.61C8.03 26.49 7.94 26.3499 7.87 26.1899C7.79999 26.0299 7.76001 25.8599 7.76001 25.6899L7.73001 23.04C7.34001 22.98 6.95001 22.8799 6.57001 22.7599C6.19001 22.6299 5.83001 22.48 5.48001 22.29C5.13001 22.1 4.79999 21.88 4.48999 21.63C4.17999 21.39 3.89 21.1199 3.63 20.82C3.37 20.52 3.13999 20.21 2.92999 19.87C2.72999 19.53 2.56001 19.18 2.42001 18.82C2.28001 18.45 2.17001 18.07 2.10001 17.69C2.03001 17.3 2 16.92 2 16.53V9.46995C2 9.03995 2.04 8.61995 2.12 8.19995C2.21 7.77995 2.34 7.36995 2.5 6.96995C2.67 6.57995 2.88 6.19995 3.12 5.84995C3.36 5.48995 3.64001 5.15995 3.95001 4.85995C4.26001 4.55995 4.59999 4.28995 4.95999 4.04995C5.32999 3.80995 5.70999 3.60995 6.10999 3.44995C6.51999 3.27995 6.94 3.15995 7.37 3.07995C7.79999 2.98995 8.23001 2.94995 8.67001 2.94995H13.3C13.46 2.94995 13.61 2.97995 13.76 3.03995C13.9 3.09995 14.03 3.17995 14.14 3.28995C14.25 3.39995 14.33 3.51995 14.39 3.65995C14.45 3.79995 14.48 3.94995 14.48 4.09995C14.48 4.25995 14.45 4.39995 14.39 4.54995C14.33 4.68995 14.25 4.80995 14.14 4.91995C14.03 5.02995 13.9 5.10995 13.76 5.16995C13.61 5.22995 13.46 5.25995 13.3 5.25995H8.67001C8.38001 5.25995 8.09999 5.27995 7.82999 5.33995C7.54999 5.38995 7.27999 5.46995 7.01999 5.57995C6.75999 5.67995 6.50999 5.80995 6.26999 5.96995C6.03999 6.11995 5.82 6.29995 5.62 6.48995C5.42 6.68995 5.23999 6.89995 5.07999 7.12995C4.92999 7.35995 4.78999 7.59995 4.67999 7.85995C4.57999 8.10995 4.49 8.37995 4.44 8.64995C4.38 8.91995 4.35999 9.18995 4.35999 9.46995V16.53C4.35999 16.81 4.38 17.08 4.44 17.36C4.5 17.63 4.58 17.9 4.69 18.16C4.8 18.42 4.93 18.67 5.09 18.9C5.25 19.13 5.43001 19.3499 5.64001 19.5499C5.84001 19.75 6.05999 19.92 6.29999 20.08C6.53999 20.24 6.79 20.37 7.06 20.47C7.32 20.58 7.6 20.66 7.88 20.72C8.16001 20.77 8.44001 20.7999 8.73001 20.7999C8.91001 20.7999 9.08 20.83 9.25 20.9C9.41 20.97 9.55999 21.0599 9.67999 21.18C9.80999 21.3099 9.91001 21.45 9.98001 21.61C10.05 21.77 10.08 21.94 10.09 22.11L10.1 23.74L13.08 21.61C13.84 21.07 14.69 20.7999 15.63 20.7999H19.32C19.61 20.7999 19.89 20.77 20.16 20.72C20.44 20.67 20.71 20.59 20.97 20.4799C21.23 20.3699 21.48 20.24 21.72 20.09C21.95 19.94 22.17 19.76 22.37 19.57C22.57 19.3699 22.75 19.16 22.91 18.93C23.07 18.7 23.2 18.46 23.31 18.2C23.41 17.95 23.5 17.68 23.55 17.41C23.61 17.14 23.63 16.87 23.63 16.59V12.94C23.63 12.79 23.66 12.64 23.72 12.5C23.78 12.36 23.87 12.23 23.98 12.13C24.09 12.02 24.22 11.93 24.36 11.88C24.51 11.82 24.66 11.79 24.82 11.79C24.97 11.79 25.12 11.82 25.27 11.88C25.41 11.93 25.54 12.02 25.65 12.13C25.76 12.23 25.85 12.36 25.91 12.5C25.97 12.64 26 12.79 26 12.94V16.59C26 17.02 25.95 17.44 25.87 17.86C25.78 18.28 25.66 18.69 25.49 19.08C25.32 19.48 25.11 19.8499 24.87 20.2099C24.63 20.57 24.35 20.9 24.04 21.2C23.73 21.5 23.39 21.7699 23.03 22.0099C22.67 22.2499 22.28 22.45 21.88 22.61C21.47 22.77 21.06 22.9 20.63 22.9799C20.2 23.07 19.76 23.11 19.32 23.11H16.4C15.47 23.11 14.62 23.3799 13.86 23.9199L9.91 26.74C9.67 26.91 9.39999 27 9.10999 27Z"
                          fill="currentColor"></path>
                    <path d="M24.6805 5.14453H18.1874C17.5505 5.14453 17.0342 5.66086 17.0342 6.29778C17.0342 6.9347 17.5505 7.45102 18.1874 7.45102H24.6805C25.3175 7.45102 25.8338 6.9347 25.8338 6.29778C25.8338 5.66086 25.3175 5.14453 24.6805 5.14453Z"
                          fill="currentColor"></path>
                    <path d="M22.6137 3.1804C22.6137 2.52848 22.0852 2 21.4333 2C20.7814 2 20.2529 2.52848 20.2529 3.1804V9.4168C20.2529 10.0687 20.7814 10.5972 21.4333 10.5972C22.0852 10.5972 22.6137 10.0687 22.6137 9.4168V3.1804Z"
                          fill="currentColor"></path>
                </svg>
            </div>
            开启新对话
        </div>

        <div id="chatSessionList" style="position: relative; margin-top: 12px">
            <ul class="history-list">
                <!-- 对话历史项将动态插入到这里 -->
            </ul>
        </div>
        <!-- 弹窗模板 -->
        <template id="menu-popup-template">
            <div class="menu-popup">
                <div class="menu-item rename">
                    <svg class="icon"
                         style="width: 1.3em;height: 1.3em;margin-right: 0.4em;vertical-align: middle;fill: currentColor;overflow: hidden;"
                         viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2371">
                        <path d="M257.7 752c2 0 4-0.2 6-0.5L431.9 722c2-0.4 3.9-1.3 5.3-2.8l423.9-423.9c3.9-3.9 3.9-10.2 0-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2c-1.9 11.1 1.5 21.9 9.4 29.8 6.6 6.4 14.9 9.9 23.8 9.9z m67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"
                              p-id="2372"></path>
                    </svg>
                    重命名
                </div>
                <div class="menu-item delete">
                    <svg class="icon"
                         style="width: 1.3em;height: 1.3em;margin-right: 0.4em;vertical-align: middle;fill: currentColor;overflow: hidden;"
                         viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1523">
                        <path d="M974.991554 109.146486 647.781177 109.146486c0-2.225921 2.225921-4.451842 2.225921-6.677763 0-55.648023-46.74434-102.392363-102.392363-102.392363l-66.777628 0C422.963163-2.14956 378.444744 44.594779 378.444744 100.242803c0 2.225921 0 4.451842 2.225921 6.677763L49.008446 106.920565c-22.259209 0-40.066577 17.807367-40.066577 40.066577s17.807367 40.066577 40.066577 40.066577l77.907233 0 0 636.613387c0 111.296047 91.262758 200.332884 200.332884 200.332884l389.536163 0c111.296047 0 200.332884-91.262758 200.332884-200.332884L917.11761 189.27964l60.099865 0c22.259209 0 40.066577-17.807367 40.066577-40.066577S997.250763 109.146486 974.991554 109.146486zM458.577898 100.242803c0-11.129605 11.129605-22.259209 22.259209-22.259209l66.777628 0c11.129605 0 22.259209 11.129605 22.259209 22.259209 0 2.225921 0 4.451842 2.225921 6.677763l-113.521968 0C456.351977 106.920565 458.577898 102.468724 458.577898 100.242803zM836.984456 825.893026c0 66.777628-53.422102 120.19973-120.19973 120.19973L327.248563 946.092757c-66.777628 0-120.19973-53.422102-120.19973-120.19973L207.048832 189.27964l629.935624 0L836.984456 825.893026zM411.833558 756.889478c22.259209 0 40.066577-17.807367 40.066577-40.066577l0-311.62893c0-22.259209-17.807367-40.066577-40.066577-40.066577s-40.066577 17.807367-40.066577 40.066577l0 311.62893C371.766981 739.08211 389.574349 756.889478 411.833558 756.889478zM632.19973 756.889478c22.259209 0 40.066577-17.807367 40.066577-40.066577l0-311.62893c0-22.259209-17.807367-40.066577-40.066577-40.066577-22.259209 0-40.066577 17.807367-40.066577 40.066577l0 311.62893C592.133154 739.08211 609.940521 756.889478 632.19973 756.889478z"
                              p-id="1524"></path>
                    </svg>
                    删除
                </div>
            </div>
        </template>

        <!-- 自定义确认弹窗模板 -->
        <template id="confirm-popup-template">
            <div class="confirm-popup">
                <p>确定要删除此对话吗？</p>
                <div class="confirm-popup-buttons">
                    <button class="confirm-no">取消</button>
                    <button class="confirm-yes">确定</button>
                </div>
            </div>
        </template>

    </div>
    <div class="home_sidebar-logo__FFdBS no-dark">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="43" height="44"
             fill="none">
            <defs>
                <path id="chatgpt_svg__a" d="M0 0h43v43.58H0z"></path>
            </defs>
            <g>
                <mask id="chatgpt_svg__b" fill="#fff">
                    <use xlink:href="#chatgpt_svg__a"></use>
                </mask>
                <g mask="url(#chatgpt_svg__b)">
                    <path fill-rule="evenodd" opacity="0.27"
                          d="M40.17 17.84c.36-1.11.55-2.27.55-3.43 0-1.93-.51-3.83-1.49-5.49a10.98 10.98 0 0 0-9.52-5.51c-.77 0-1.55.08-2.3.24A10.868 10.868 0 0 0 19.29 0h-.1c-4.76 0-8.98 3.07-10.45 7.6-3.06.63-5.71 2.55-7.26 5.27a10.993 10.993 0 0 0 1.35 12.87c-.36 1.11-.55 2.27-.55 3.43 0 1.93.51 3.83 1.49 5.49a10.97 10.97 0 0 0 11.82 5.27c2.06 2.32 5.02 3.65 8.12 3.65h.1c4.76 0 8.99-3.07 10.45-7.61a10.82 10.82 0 0 0 7.26-5.26 10.995 10.995 0 0 0-1.35-12.87ZM18.817 38.695c-.09.05-.17.1-.26.15a8.145 8.145 0 0 0 5.22 1.89h.01c4.5-.01 8.15-3.67 8.16-8.17v-10.13a.153.153 0 0 0-.07-.1l-3.67-2.12v12.24c0 .51-.27.98-.72 1.23l-8.67 5.01Zm-1.424-2.472 8.77-5.06c.04-.03.06-.07.06-.11h-.01v-4.24l-10.59 6.12c-.44.25-.98.25-1.42 0l-8.68-5.01c-.08-.05-.2-.12-.26-.16a8.19 8.19 0 0 0 .97 5.47 8.18 8.18 0 0 0 7.08 4.08c1.43 0 2.84-.37 4.08-1.09Zm-9.187-25.21v-.3c-1.79.66-3.3 1.93-4.25 3.58a8.226 8.226 0 0 0-1.09 4.08c0 2.92 1.55 5.61 4.08 7.07l8.77 5.07c.04.02.09.02.12-.01l3.67-2.12-10.59-6.11c-.44-.25-.71-.72-.71-1.23v-10.03Zm27.849 7.117-8.77-5.07a.126.126 0 0 0-.12.01l-3.67 2.12 10.59 6.12c.44.25.71.71.71 1.22v10.33a8.168 8.168 0 0 0 5.35-7.66 8.16 8.16 0 0 0-4.09-7.07Zm-19.22-5.718a.16.16 0 0 0-.05.11v4.24l10.59-6.12c.22-.12.47-.19.72-.19s.49.07.71.19l8.68 5.02c.08.05.17.1.25.15.08-.46.12-.92.12-1.38 0-4.51-3.66-8.17-8.17-8.17-1.43 0-2.83.38-4.08 1.09l-8.77 5.06ZM19.22 2.85c-4.51 0-8.17 3.65-8.17 8.16v10.13c.01.05.03.08.07.1l3.67 2.12.01-12.23v-.01c0-.5.27-.97.71-1.22l8.68-5.01c.07-.05.19-.11.25-.15a8.145 8.145 0 0 0-5.22-1.89ZM16.783 24.51l4.72 2.73 4.72-2.73v-5.45l-4.72-2.72-4.72 2.73v5.44Z"
                          style="fill: var(--pai-brand-1-normal);"></path>
                </g>
            </g>
        </svg>
    </div>
</div>

<div class="login-guide-wrap">
    <div class="home_sidebar-title__d8_c_">加入星球后可获得以下权益</div>
    <ul class="login-guide-list">
        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="login-guide-icon">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M13.2368 3.03451C12.587 2.18471 11.3966 2.05398 10.5779 2.74252L8.99865 4.07065L7.41943 2.74252C6.60071 2.05398 5.41026 2.18471 4.76048 3.03451C4.40851 3.49484 4.28737 4.07174 4.38041 4.61328H3.42708C2.6907 4.61328 2.09375 5.21023 2.09375 5.94661V7.78026C2.09375 8.50852 2.67761 9.10041 3.40278 9.11338V14.285C3.40278 15.3896 4.29821 16.285 5.40278 16.285H12.5945C13.6991 16.285 14.5945 15.3896 14.5945 14.285V9.11342C15.3209 9.10187 15.9062 8.50942 15.9062 7.78026V5.94661C15.9062 5.21024 15.3093 4.61328 14.5729 4.61328H13.6169C13.7099 4.07174 13.5888 3.49484 13.2368 3.03451ZM12.61 4.61328C12.7209 4.3068 12.6796 3.95035 12.4715 3.67815C12.1572 3.26708 11.5813 3.20384 11.1853 3.53691L9.90541 4.61328H12.61ZM8.09189 4.61328L6.81202 3.53691C6.41598 3.20384 5.84013 3.26708 5.52581 3.67815C5.31768 3.95035 5.27642 4.3068 5.38727 4.61328H8.09189ZM3.09375 5.94661C3.09375 5.76252 3.24299 5.61328 3.42708 5.61328H8.49863V8.11359H3.42708C3.24299 8.11359 3.09375 7.96435 3.09375 7.78026V5.94661ZM9.49863 8.11359V5.61328H14.5729C14.757 5.61328 14.9062 5.76252 14.9062 5.94661V7.78026C14.9062 7.96435 14.757 8.11359 14.5729 8.11359H9.49863ZM4.40278 9.11719H8.49863L8.49863 15.285H5.40278C4.85049 15.285 4.40278 14.8373 4.40278 14.285V9.11719ZM9.49863 9.11719L9.49863 15.285H12.5945C13.1468 15.285 13.5945 14.8373 13.5945 14.285V9.11719H9.49863Z"
                          fill="currentColor"></path>
                </svg>
            </div>
            <span class="login-guide-text">畅玩派聪明，已接入DeepSeek、openai、讯飞星火、智谱</span>
        </li>
        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="login-guide-icon">
                    <path d="M4.62218 2.68165C3.64897 2.29236 2.59033 3.0091 2.59033 4.05728V13.961C2.59033 14.2639 2.77476 14.5363 3.05601 14.6488L8.88416 16.98C9.59939 17.2661 10.3973 17.2661 11.1125 16.98L16.9406 14.6488C17.2219 14.5363 17.4063 14.2639 17.4063 13.961V4.05728C17.4063 3.0091 16.3477 2.29236 15.3745 2.68165L11.1125 4.38644C10.3973 4.67253 9.59939 4.67253 8.88416 4.38644L4.62218 2.68165Z"
                          stroke="currentColor" stroke-width="1.2"></path>
                    <path d="M9.99609 7.42676V14.094" stroke="currentColor" stroke-width="1.2"
                          stroke-linecap="round"></path>
                </svg>
            </div>
            <span class="login-guide-text">
                <a target="_blank" href="https://javabetter.cn/zhishixingqiu/mianshi.html">精心打造的面试求职攻略，助你拿到大厂 offer</a>
            </span>
        </li>
        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="login-guide-icon">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M12.1337 10.5801C12.4205 10.5801 12.653 10.8125 12.653 11.0993V11.8658H13.4194C13.7062 11.8658 13.9387 12.0983 13.9387 12.385C13.9387 12.6718 13.7062 12.9043 13.4194 12.9043H12.1337C11.847 12.9043 11.6145 12.6718 11.6145 12.385V11.0993C11.6145 10.8125 11.847 10.5801 12.1337 10.5801Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M2.05225 4.64889C2.05225 3.21507 3.21458 2.05273 4.6484 2.05273H11.4231C12.8569 2.05273 14.0193 3.21507 14.0193 4.64889V9.32197H12.9808V4.64889C12.9808 3.7886 12.2834 3.0912 11.4231 3.0912H4.6484C3.78811 3.0912 3.09071 3.7886 3.09071 4.64889V13.3522C3.09071 14.2125 3.78811 14.9099 4.6484 14.9099H12.2143V15.9483H4.6484C3.21459 15.9483 2.05225 14.786 2.05225 13.3522V4.64889Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M12.214 9.51893C10.7255 9.51893 9.51893 10.7255 9.51893 12.214C9.51893 13.7024 10.7255 14.909 12.214 14.909C13.7024 14.909 14.909 13.7024 14.909 12.214C14.909 10.7255 13.7024 9.51893 12.214 9.51893ZM8.48047 12.214C8.48047 10.152 10.152 8.48047 12.214 8.48047C14.2759 8.48047 15.9475 10.152 15.9475 12.214C15.9475 14.2759 14.2759 15.9475 12.214 15.9475C10.152 15.9475 8.48047 14.2759 8.48047 12.214Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M4.82129 5.4372C4.82129 5.15044 5.05376 4.91797 5.34052 4.91797L10.7306 4.91797C11.0174 4.91797 11.2499 5.15044 11.2499 5.4372C11.2499 5.72396 11.0174 5.95643 10.7306 5.95643L5.34052 5.95643C5.05376 5.95643 4.82129 5.72396 4.82129 5.4372Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M4.78394 8.35712C4.78394 8.07035 5.01641 7.83789 5.30317 7.83789L8.03531 7.83791C8.32208 7.83791 8.55454 8.07038 8.55454 8.35715C8.55454 8.64391 8.32207 8.87637 8.03531 8.87637L5.30316 8.87635C5.0164 8.87635 4.78393 8.64388 4.78394 8.35712Z"
                          fill="currentColor"></path>
                </svg>
            </div>
            <span class="login-guide-text">
                <a target="_blank" href="https://javabetter.cn/zhishixingqiu/paicoding.html">手把手教你玩转技术派实战项目，120 篇+硬核教程</a>
            </span>
        </li>
        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="login-guide-icon">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8.99999 3.37781L7.26101 6.9014C7.15913 7.10768 6.96219 7.25119 6.73392 7.28433L2.84544 7.84938L5.65897 10.5919C5.65903 10.592 5.6589 10.5919 5.65897 10.5919C5.82418 10.7529 5.89936 10.9849 5.8605 11.2116L5.19624 15.0845L8.67423 13.256C8.87818 13.1488 9.12181 13.1488 9.32576 13.256L12.8037 15.0845L12.1395 11.2117C12.1006 10.9849 12.1756 10.7531 12.3408 10.5921C12.3409 10.592 12.3408 10.5922 12.3408 10.5921L15.1545 7.84938L11.2663 7.28437C11.038 7.25122 10.8409 7.10782 10.739 6.90153L8.99999 3.37781ZM9.26883 2.83308C9.26888 2.83298 9.26878 2.83318 9.26883 2.83308V2.83308ZM9.62775 2.39022C9.37097 1.86993 8.62901 1.86993 8.37224 2.39022L6.43408 6.3174L2.10014 6.94718C1.52608 7.03061 1.29661 7.73615 1.71217 8.14119C1.71216 8.14118 1.71219 8.14121 1.71217 8.14119L4.84822 11.1981L4.10789 15.5145C4.00989 16.0863 4.61 16.5224 5.12357 16.2525L8.99999 14.2145L12.8764 16.2525C13.3899 16.5224 13.9901 16.0863 13.8921 15.5146L13.1518 11.1981L16.2877 8.14126C16.7035 7.73618 16.4739 7.0306 15.8999 6.94718L11.5659 6.3174L9.62775 2.39022ZM4.96134 11.3084C4.96141 11.3084 4.96128 11.3083 4.96134 11.3084V11.3084Z"
                          fill="currentColor"></path>
                </svg>
            </div>
            <span class="login-guide-text">
                <a target="_blank" href="https://javabetter.cn/zhishixingqiu/jianli.html">贴心的简历指导，打造一份投了就有面试的精美简历，亮瞎HR的狗眼</a>
            </span>
        </li>

        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="login-guide-icon">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M5.21627 3.23729H2.91894L2.91894 14.2645H5.21627V3.23729ZM2.91894 2.31836C2.41142 2.31836 2 2.72978 2 3.23729V14.2645C2 14.772 2.41142 15.1835 2.91894 15.1835H5.21627C5.72379 15.1835 6.13521 14.772 6.13521 14.2645V3.23729C6.13521 2.72978 5.72379 2.31836 5.21627 2.31836H2.91894Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M8.43258 3.23729H6.13524L6.13524 14.2645H8.43258V3.23729ZM6.13524 2.31836C5.62773 2.31836 5.21631 2.72978 5.21631 3.23729V14.2645C5.21631 14.772 5.62773 15.1835 6.13524 15.1835H8.43258C8.9401 15.1835 9.35152 14.772 9.35152 14.2645V3.23729C9.35152 2.72978 8.9401 2.31836 8.43258 2.31836H6.13524Z"
                          fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M12.0205 3.1255L9.80149 3.7201L12.6555 14.3716L14.8746 13.777L12.0205 3.1255ZM9.56365 2.83247C9.07343 2.96383 8.78251 3.46772 8.91386 3.95794L11.7679 14.6094C11.8993 15.0996 12.4032 15.3906 12.8934 15.2592L15.1124 14.6646C15.6027 14.5333 15.8936 14.0294 15.7622 13.5391L12.9082 2.88767C12.7768 2.39744 12.2729 2.10653 11.7827 2.23788L9.56365 2.83247Z"
                          fill="currentColor"></path>
                </svg>
            </div>
            <span class="login-guide-text">
                <a target="_blank" href="https://javabetter.cn/zhishixingqiu/">面渣逆袭.pdf，让天下没有难刷的八股文</a>
            </span>
        </li>
        <li class="login-guide-list-item">
            <div class="login-guide-icon-wrap">
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="user-level-icon login-guide-icon">
                    <path d="M17.2673 10.1562H19.7673V12.6562" stroke="currentColor" stroke-linecap="round"
                          stroke-linejoin="round"></path>
                    <path d="M8.5625 8.25V18.375C8.5625 18.9273 9.01022 19.375 9.5625 19.375H20.4375"
                          stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M10.5625 16.0938L13.888 12.36L16.0216 14.2603L18.9739 10.9455" stroke="currentColor"
                          stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </div>
            <span class="login-guide-text">
                <a target="_blank"
                   href="https://javabetter.cn/zhishixingqiu/">向二哥提问、帮你制定学习计划、告别迷茫和焦虑</a>
            </span>
        </li>
    </ul>
</div>
<!-- 立即购买 button -->
<div class="uno-buy-card-wrap">
    <img th:src="${global.siteInfo.zsxqPosterUrl}" alt="二哥的编程星球，扫码加入" class="qrcode-img">
</div>

<script th:inline="javascript">
    function uuid(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [], i;
        radix = radix || chars.length;

        if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
        } else {
            // rfc4122, version 4 form
            var r;

            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';

            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }

        return uuid.join('');
    }

    const historyList = document.querySelector('.history-list');
    const menuPopupTemplate = document.getElementById('menu-popup-template');
    const confirmPopupTemplate = document.getElementById('confirm-popup-template');

    // 点击事件
    $('#newChatBtn').click(() => newChat());

    function newChat() {
        if ([[${global.isLogin}]]) {
            // 生成一个新的对话的场景
            const chatId = uuid(64, 16);
            const li = addChatSession({
                'title': '新对话',
                'chatId': chatId
            })
            historyList.prepend(li);
            chatSessionChoosed(li);
            return chatId;
        } else {
            toastr.warning("请登录之后再体验吧~");
        }
    }


    // 获取用户的对话记录，并在左侧边栏进行展示
    function listChatSessions() {
        if ([[${global.isLogin}]]) {
            let aiType = $('#chat-type').val();
            $.get('/chat/api/listSession?aiType=' + aiType, function (data) {
                console.log("获取到的对话记录>>>:", data);
                if (!data || !data.status || data.status.code !== 0) {
                    toastr.error(data.message);
                } else {
                    updateChatSessions(data.result)
                }
            });
        }
    }

    /**
     * 更新用户的对话历史
     */
    function updateChatSessions(data) {
        if (!data) {
            // 没有数据时，直接返回，此时需要创建新的对话进行交流
            return;
        }
        // 清空之前的历史记录
        historyList.innerHTML = '';

        let firstLi = null;
        let i = 0;
        for (let chat of data) {
            const li = addChatSession(chat);
            historyList.appendChild(li);
            if (i === 0) firstLi = li;
            i++;
        }
        if (firstLi) {
            // 默认选中第一个进行对话
            chatSessionChoosed(firstLi);
        }
    }

    /**
     * 创建一个聊天会话
     * @param chat
     * @return {HTMLLIElement}
     */
    function addChatSession(chat) {
        const li = document.createElement('li');

        // 新增一个div元素来包裹对话名
        const chatTitle = document.createElement('div');
        chatTitle.className = 'chat-title';
        chatTitle.textContent = chat.title;
        li.appendChild(chatTitle);

        li.dataset.id = chat.chatId;
        li.dataset.title = chat.title;

        // 添加菜单按钮
        const menuButton = document.createElement('div');
        menuButton.innerHTML = "<div style=\"font-size: 16px; width: 16px; height: 16px;\"><svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0\" clip-rule=\"evenodd\"></path></svg></div>"
        menuButton.className = 'menu-button';
        // menuButton.textContent = '···';
        li.appendChild(menuButton);

        // 点击对话项加载聊天记录
        li.addEventListener('click', () => {
            chatSessionChoosed(li);
        });

        // 点击菜单按钮显示弹窗
        menuButton.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            showMenuPopup(li, chat.chatId);
        });
        return li;
    }

    function chatSessionChoosed(li) {
        // 移除其他项的高亮
        document.querySelectorAll('.history-list li').forEach(item => {
            item.classList.remove('active');
        });
        // 高亮当前项
        li.classList.add('active');
        loadChat(li.dataset.id);
    }


    // 加载聊天记录
    let lastChatId = null;

    function loadChat(chatId) {
        if (!wsConnected) {
            // ws未建立，直接返回
            console.log("loadChat---> ws未连接，无法同步历史会话内容!");
            return;
        }

        if (chatId == null) {
            // ws建立成功之后的回调，进入这里，从选中的会话中获取chatId
            var li = document.querySelector(".history-list li.active")
            if (li) {
                chatId = li.dataset.id;
            }
            console.log("loadChat---> ws连接成功，自动选择当前会话:", chatId, li);
        }

        if (chatId === lastChatId) {
            console.log('loadChat---> 两次传入的chatId相同，自动幂等!', chatId);
            return;
        }
        lastChatId = chatId;

        let aiType = $('#chat-type').val();
        // 清空之前的聊天记录
        $("#chat-content").html('')
        $.get('/chat/api/syncHistory?aiType=' + aiType + '&chatId=' + chatId, function (data) {
            console.log("loadChat---> 同步聊天历史: ", data);
        });
    }

    // 显示菜单弹窗
    function showMenuPopup(li, chatId) {
        // 移除已有的弹窗
        const existingPopup = document.querySelector('.menu-popup');
        if (existingPopup) existingPopup.remove();

        // 创建弹窗
        const popup = menuPopupTemplate.content.cloneNode(true).querySelector('.menu-popup');
        li.appendChild(popup);

        // 重命名功能
        popup.querySelector('.rename').addEventListener('click', (e) => {
            e.stopPropagation();
            // 创建输入框
            const input = document.createElement('input');
            input.type = 'text';
            input.value = li.dataset.title;
            input.style.width = '100%';
            li.innerHTML = '';
            li.appendChild(input);
            input.focus();

            // 处理输入框的确认事件
            const confirmRename = () => {
                const newTitle = input.value.trim();
                if (newTitle && newTitle != li.dataset.title) {
                    // 新增一个div元素来包裹对话名
                    const chatTitle = document.createElement('div');
                    chatTitle.className = 'chat-title';
                    chatTitle.textContent = newTitle;
                    li.appendChild(chatTitle);

                    li.dataset.title = newTitle;
                    let aiType = $('#chat-type').val();
                    $.get(`/chat/api/updateSession?aiType=${aiType}&chatId=${chatId}&title=${newTitle}`, function (data) {
                        console.log('更新会话标题');
                    });
                } else {
                    // 使用原来的标题
                    const chatTitle = document.createElement('div');
                    chatTitle.className = 'chat-title';
                    chatTitle.textContent = li.dataset.title;
                    li.appendChild(chatTitle);
                }

                // 移除输入框
                input.remove();
                // 重新添加菜单按钮
                const menuButton = document.createElement('div');
                menuButton.className = 'menu-button';
                menuButton.innerHTML = "<div style=\"font-size: 16px; width: 16px; height: 16px;\"><svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M3 12a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0m7 0a2 2 0 1 1 4 0 2 2 0 0 1-4 0\" clip-rule=\"evenodd\"></path></svg></div>"
                li.appendChild(menuButton);
                // 绑定菜单按钮事件
                menuButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showMenuPopup(li, chatId);
                });
            };

            // 监听输入框的 Enter 键
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    confirmRename();
                }
            });

            // 监听输入框的失去焦点事件
            input.addEventListener('blur', confirmRename);

            popup.remove();
        });

        // 删除功能
        popup.querySelector('.delete').addEventListener('click', (e) => {
            e.stopPropagation();
            const confirmPopup = confirmPopupTemplate.content.cloneNode(true).querySelector('.confirm-popup');
            document.body.appendChild(confirmPopup);

            // 确定按钮点击事件
            confirmPopup.querySelector('.confirm-yes').addEventListener('click', () => {
                let aiType = $('#chat-type').val();
                $.get(`/chat/api/updateSession?aiType=${aiType}&chatId=${chatId}&deleted=true`, function (data) {
                    if (data && data.result) {
                        li.remove();
                        if (li.dataset.id === lastChatId) {
                            // 当前删除的是选中的会话，需要重新选第一个进行激活
                            let firstLi = document.querySelector('.history-list li');
                            if (firstLi) {
                                chatSessionChoosed(firstLi);
                            } else {
                                // 所有的对话都删除了，需要重新建一个
                                newChat()
                            }
                        }
                    } else {
                        toastr.error('删除对话失败!');
                    }
                });

                confirmPopup.remove();
            });

            // 取消按钮点击事件
            confirmPopup.querySelector('.confirm-no').addEventListener('click', () => {
                confirmPopup.remove();
            });

            popup.remove();
        });

        // 点击其他地方关闭弹窗
        document.addEventListener('click', () => {
            popup.remove();
        }, {once: true});
    }


    document.addEventListener('DOMContentLoaded', function () {
        listChatSessions();
    });
</script>