<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
  <!-- 专栏详情 -->
  <div th:fragment="column_card(column)">
    <a
      th:href="'/column/' + ${column.columnId} + '/1'"
      class="item"
    >
      <div class="poster">
        <img
          th:src="${column.cover}"
          th:alt="'「' + ${column.column} + '」封面'"
          class="lazy poster-img"
          style=""
          loading="lazy"
        />
      </div>
      <div class="info">
        <div class="title">
          <span class="new-tag-wrap tag" th:if="${column.type == 2}">上新</span>
          <span class="text-highlight" th:text="${column.column}">
            分布式专栏
          </span>
        </div>

        <div class="author">
          <object>
            <a
              th:href="'/user/' + ${column.author}"
              class="user"
            >
              <img th:src="${column.authorAvatar}"
                src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fdpic.tiankong.com%2Fet%2Fjg%2FQJ9108977402.jpg&refer=http%3A%2F%2Fdpic.tiankong.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1664861850&t=281d7109272f158ecb9fbee9c0fd0efb"
                class="lazy avatar hero"
                loading="lazy"
              />
              <span
                class="name"
                th:text="${column.authorName}"
              >
                一灰
              </span>

              <span class="self-description">
                |
              </span>

              <span class="self-description"
                    th:text="${column.authorProfile != null && !column.authorProfile.isEmpty() ? column.authorProfile : '这人很懒，还没留下简介~'}">
                  前端技术专家
              </span>

            </a>
          </object>
        </div>
        <div class="other">
          <div class="messages">
            <span class="message">
              <span class="article-count"
                    th:text="(${column.state == 2} ? '已完结 ' : '已更新 ') + ${column.count.articleCount} + ' 小节'">
                已更新21小节
              </span>
              <span class="read-count"
                    th:text="'共 ' + ${column.count.totalNums} + ' 节'">
                共100节
              </span>
              <span class="read-count" th:text="${column.count.readCount} + ' 人已阅读'">
                多少人阅读
              </span>
            </span>
            <span class="sale-tooltip" th:if="${column.type == 2 && new java.util.Date().getTime() < column.freeEndTime }" >
              <span th:text="${column.type == 2} ? '限时免费': '免费'"></span>
              <span class="count-down-text"
                    th:data-free-endTime="${column.freeEndTime}"
                    th:data-free-startTime="${column.freeStartTime}"
                    th:text="''">
                    剩余 1 天 12:50:43.92
              </span>
            </span>
          </div>
        </div>

        <span class="text-highlight desc" th:text="${column.introduction}">
          一个简单的分布式专栏
        </span>
      </div>
    </a>
  </div>
</html>
